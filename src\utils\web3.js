import { ethers } from 'ethers';
import { STUDENT_MANAGEMENT_ABI, CONTRACT_CONFIG } from '../config/contract';

class Web3Service {
  constructor() {
    this.provider = null;
    this.signer = null;
    this.contract = null;
    this.account = null;
  }

  // 检查MetaMask是否安装
  isMetaMaskInstalled() {
    return typeof window !== 'undefined' && typeof window.ethereum !== 'undefined';
  }

  // 连接MetaMask钱包
  async connectWallet() {
    if (!this.isMetaMaskInstalled()) {
      throw new Error('请安装MetaMask钱包');
    }

    try {
      // 请求账户访问权限
      await window.ethereum.request({ method: 'eth_requestAccounts' });
      
      // 创建provider和signer
      this.provider = new ethers.BrowserProvider(window.ethereum);
      this.signer = await this.provider.getSigner();
      this.account = await this.signer.getAddress();

      // 检查网络
      await this.checkNetwork();

      // 初始化合约实例
      this.initContract();

      return this.account;
    } catch (error) {
      console.error('连接钱包失败:', error);
      throw error;
    }
  }

  // 检查并切换到本地网络
  async checkNetwork() {
    const network = await this.provider.getNetwork();
    const targetChainId = parseInt(CONTRACT_CONFIG.NETWORK_CONFIG.chainId, 16);

    if (Number(network.chainId) !== targetChainId) {
      try {
        // 尝试切换网络
        await window.ethereum.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: CONTRACT_CONFIG.NETWORK_CONFIG.chainId }],
        });
      } catch (switchError) {
        // 如果网络不存在，添加网络
        if (switchError.code === 4902) {
          await window.ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [CONTRACT_CONFIG.NETWORK_CONFIG],
          });
        } else {
          throw switchError;
        }
      }
    }
  }

  // 初始化合约实例
  initContract() {
    if (!this.signer) {
      throw new Error('请先连接钱包');
    }

    this.contract = new ethers.Contract(
      CONTRACT_CONFIG.STUDENT_MANAGEMENT_ADDRESS,
      STUDENT_MANAGEMENT_ABI,
      this.signer
    );
  }

  // 获取当前连接的账户
  async getCurrentAccount() {
    if (!this.provider) {
      return null;
    }

    try {
      const accounts = await window.ethereum.request({ method: 'eth_accounts' });
      return accounts[0] || null;
    } catch (error) {
      console.error('获取账户失败:', error);
      return null;
    }
  }

  // 监听账户变化
  onAccountsChanged(callback) {
    if (window.ethereum) {
      window.ethereum.on('accountsChanged', callback);
    }
  }

  // 监听网络变化
  onChainChanged(callback) {
    if (window.ethereum) {
      window.ethereum.on('chainChanged', callback);
    }
  }

  // 断开连接
  disconnect() {
    this.provider = null;
    this.signer = null;
    this.contract = null;
    this.account = null;
  }

  // 获取所有学生
  async getAllStudents() {
    if (!this.contract) {
      throw new Error('合约未初始化');
    }

    try {
      const students = await this.contract.getAllStudents();
      return students.map(student => ({
        id: Number(student.id),
        name: student.name,
        age: Number(student.age),
        major: student.major,
        isActive: student.isActive
      }));
    } catch (error) {
      console.error('获取学生列表失败:', error);
      throw error;
    }
  }

  // 获取单个学生信息
  async getStudent(id) {
    if (!this.contract) {
      throw new Error('合约未初始化');
    }

    try {
      const student = await this.contract.getStudent(id);
      return {
        id: Number(student.id),
        name: student.name,
        age: Number(student.age),
        major: student.major,
        isActive: student.isActive
      };
    } catch (error) {
      console.error('获取学生信息失败:', error);
      throw error;
    }
  }

  // 添加学生
  async addStudent(id, name, age, major) {
    if (!this.contract) {
      throw new Error('合约未初始化');
    }

    try {
      const tx = await this.contract.addStudent(id, name, age, major);
      await tx.wait();
      return tx;
    } catch (error) {
      console.error('添加学生失败:', error);
      throw error;
    }
  }

  // 更新学生信息
  async updateStudent(id, name, age, major) {
    if (!this.contract) {
      throw new Error('合约未初始化');
    }

    try {
      const tx = await this.contract.updateStudent(id, name, age, major);
      await tx.wait();
      return tx;
    } catch (error) {
      console.error('更新学生信息失败:', error);
      throw error;
    }
  }

  // 删除学生
  async deleteStudent(id) {
    if (!this.contract) {
      throw new Error('合约未初始化');
    }

    try {
      const tx = await this.contract.deleteStudent(id);
      await tx.wait();
      return tx;
    } catch (error) {
      console.error('删除学生失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const web3Service = new Web3Service();
export default web3Service;
