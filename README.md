# 去中心化学生管理系统

基于以太坊区块链的去中心化学生信息管理系统，使用Next.js和ethers.js构建。

## 功能特性

- 🔗 **区块链集成**: 使用以太坊智能合约存储学生数据
- 🦊 **MetaMask支持**: 集成MetaMask钱包进行身份验证
- 📝 **完整CRUD操作**: 支持学生信息的增加、查询、更新和删除
- 🎨 **现代化UI**: 使用Tailwind CSS构建响应式界面
- 🔒 **去中心化**: 数据存储在区块链上，确保透明性和不可篡改性

## 技术栈

- **前端**: Next.js, React, Tailwind CSS
- **区块链**: Ethereum, Solidity, ethers.js
- **钱包**: MetaMask
- **开发工具**: Remix IDE

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置智能合约

1. 在Remix IDE中部署 `contracts/StudentManagement.sol` 合约
2. 复制部署后的合约地址
3. 在 `src/config/contract.js` 中更新合约地址：

```javascript
export const CONTRACT_CONFIG = {
  STUDENT_MANAGEMENT_ADDRESS: "你的合约地址", // 替换为实际地址
  // ...其他配置
};
```

### 3. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 4. 连接钱包

1. 确保已安装MetaMask浏览器扩展
2. 配置MetaMask连接到你的本地区块链网络
3. 在应用中点击"连接MetaMask"

## 项目结构

```
├── src/
│   ├── components/          # React组件
│   │   ├── WalletConnect.js # 钱包连接组件
│   │   └── StudentManagement.js # 学生管理组件
│   ├── config/
│   │   └── contract.js      # 智能合约配置
│   ├── utils/
│   │   └── web3.js         # Web3工具类
│   ├── pages/
│   │   └── index.js        # 主页面
│   └── styles/
│       └── globals.css     # 全局样式
├── contracts/
│   └── StudentManagement.sol # 智能合约源码
├── CONTRACT_SETUP.md       # 合约配置指南
└── README.md
```

## 智能合约接口

### 主要函数

- `addStudent(id, name, age, major)` - 添加学生
- `updateStudent(id, name, age, major)` - 更新学生信息
- `deleteStudent(id)` - 删除学生
- `getStudent(id)` - 获取单个学生信息
- `getAllStudents()` - 获取所有学生列表

### 事件

- `StudentAdded` - 学生添加事件
- `StudentUpdated` - 学生更新事件
- `StudentDeleted` - 学生删除事件

## 使用说明

### 添加学生
1. 连接MetaMask钱包
2. 点击"添加学生"按钮
3. 填写学生信息（ID、姓名、年龄、专业）
4. 确认交易

### 编辑学生
1. 在学生列表中点击"编辑"按钮
2. 修改学生信息
3. 确认交易

### 删除学生
1. 在学生列表中点击"删除"按钮
2. 确认删除操作
3. 确认交易

## 开发指南

### 本地区块链设置

推荐使用Ganache作为本地区块链：

1. 安装Ganache
2. 启动Ganache，默认端口8545
3. 在MetaMask中添加本地网络：
   - 网络名称: Local Blockchain
   - RPC URL: http://127.0.0.1:8545
   - 链ID: 1337
   - 货币符号: ETH

### 合约部署

1. 在Remix IDE中打开 `contracts/StudentManagement.sol`
2. 编译合约
3. 连接到本地网络
4. 部署合约
5. 复制合约地址到配置文件

## 故障排除

### 常见问题

1. **合约未配置**: 确保在 `src/config/contract.js` 中设置了正确的合约地址
2. **网络连接失败**: 检查本地区块链是否运行，MetaMask网络配置是否正确
3. **交易失败**: 确保账户有足够的ETH支付gas费用

### 调试技巧

- 打开浏览器开发者工具查看控制台错误
- 检查MetaMask的交易历史
- 确认合约函数调用参数正确

## 安全注意事项

- 仅在测试环境中使用
- 不要在主网部署未经审计的合约
- 保护好私钥和助记词
- 定期备份重要数据

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
