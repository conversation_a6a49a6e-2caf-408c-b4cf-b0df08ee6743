# 去中心化学生管理系统 - 合约配置指南

## 概述
这是一个基于以太坊区块链的去中心化学生管理系统，使用Next.js作为前端框架，ethers.js进行区块链交互。

## 前置要求
1. 已安装MetaMask浏览器扩展
2. 已在Remix IDE上编译和部署智能合约
3. 本地区块链环境（如Ganache）正在运行

## 配置步骤

### 1. 配置合约地址
在 `src/config/contract.js` 文件中，将 `STUDENT_MANAGEMENT_ADDRESS` 替换为你在Remix上部署的实际合约地址：

```javascript
export const CONTRACT_CONFIG = {
  // 将此地址替换为你在Remix上部署的合约地址
  STUDENT_MANAGEMENT_ADDRESS: "0x你的合约地址",
  
  // 本地链配置
  NETWORK_CONFIG: {
    chainId: "0x539", // 1337 in hex (Ganache default)
    chainName: "Local Blockchain",
    nativeCurrency: {
      name: "ETH",
      symbol: "ETH",
      decimals: 18
    },
    rpcUrls: ["http://127.0.0.1:8545"], // 本地链RPC地址
    blockExplorerUrls: null
  }
};
```

### 2. 智能合约ABI
如果你的智能合约接口与预设的ABI不同，请在 `src/config/contract.js` 中更新 `STUDENT_MANAGEMENT_ABI`。

### 3. 网络配置
如果你使用的本地链配置不同，请相应修改 `NETWORK_CONFIG`：
- `chainId`: 你的本地链ID
- `rpcUrls`: 你的本地链RPC地址

## 智能合约要求
系统期望智能合约包含以下函数：

```solidity
// 学生结构体
struct Student {
    uint256 id;
    string name;
    uint256 age;
    string major;
    bool isActive;
}

// 必需的函数
function addStudent(uint256 _id, string memory _name, uint256 _age, string memory _major) public;
function updateStudent(uint256 _id, string memory _name, uint256 _age, string memory _major) public;
function deleteStudent(uint256 _id) public;
function getStudent(uint256 _id) public view returns (Student memory);
function getAllStudents() public view returns (Student[] memory);

// 事件
event StudentAdded(uint256 indexed id, string name, uint256 age, string major);
event StudentUpdated(uint256 indexed id, string name, uint256 age, string major);
event StudentDeleted(uint256 indexed id);
```

## 使用说明

### 1. 启动应用
```bash
npm run dev
```

### 2. 连接钱包
- 确保MetaMask已安装并解锁
- 点击"连接MetaMask"按钮
- 如果需要，系统会自动提示添加或切换到本地网络

### 3. 管理学生
- **添加学生**: 点击"添加学生"按钮，填写学生信息
- **编辑学生**: 点击学生列表中的"编辑"按钮
- **删除学生**: 点击学生列表中的"删除"按钮

## 故障排除

### 1. 合约未配置错误
如果看到"合约地址未配置"的提示，请检查：
- `src/config/contract.js` 中的合约地址是否正确
- 合约地址不能是 `******************************************`

### 2. 网络连接问题
如果无法连接到区块链：
- 确保本地区块链（如Ganache）正在运行
- 检查RPC地址是否正确
- 确保MetaMask连接到正确的网络

### 3. 交易失败
如果交易失败：
- 确保账户有足够的ETH支付gas费用
- 检查合约函数调用是否正确
- 查看浏览器控制台的错误信息

## 开发说明
- 所有区块链交互都通过 `src/utils/web3.js` 中的 `Web3Service` 类处理
- 钱包连接逻辑在 `src/components/WalletConnect.js` 中
- 学生管理界面在 `src/components/StudentManagement.js` 中
- 合约配置在 `src/config/contract.js` 中

## 安全注意事项
- 仅在测试环境中使用
- 不要在主网上部署未经充分测试的合约
- 确保私钥安全，不要泄露助记词
