import { useState } from 'react';
import WalletConnect from '../components/WalletConnect';
import StudentManagement from '../components/StudentManagement';
import ConfigGuide from '../components/ConfigGuide';

export default function Home() {
  const [connectedAccount, setConnectedAccount] = useState(null);

  const handleAccountChange = (account) => {
    setConnectedAccount(account);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-10 w-10 bg-blue-600 rounded-lg flex items-center justify-center">
                  <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h1 className="text-2xl font-bold text-gray-900">
                  去中心化学生管理系统
                </h1>
                <p className="text-gray-600 text-sm">
                  基于以太坊区块链的学生信息管理平台
                </p>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              <div className="flex items-center text-sm text-gray-500">
                <div className="h-2 w-2 bg-green-400 rounded-full mr-2"></div>
                区块链网络
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 钱包连接区域 */}
        <div className="mb-8">
          <WalletConnect onAccountChange={handleAccountChange} />
        </div>

        {/* 学生管理区域 */}
        <StudentManagement account={connectedAccount} />
      </main>

      {/* 页脚 */}
      <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex justify-center items-center mb-4">
              <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <span className="text-lg font-semibold text-gray-900">区块链技术驱动</span>
            </div>
            <p className="text-gray-600 mb-2">© 2024 去中心化学生管理系统. 基于以太坊区块链技术.</p>
            <p className="text-sm text-gray-500">
              请确保已连接到正确的本地区块链网络 | 数据安全存储在区块链上
            </p>
            <div className="mt-4 flex justify-center space-x-6 text-sm text-gray-500">
              <span>✓ 去中心化存储</span>
              <span>✓ 数据不可篡改</span>
              <span>✓ 透明可追溯</span>
            </div>
          </div>
        </div>
      </footer>

      {/* 配置指南 */}
      <ConfigGuide />
    </div>
  );
}
