import { useState } from 'react';
import WalletConnect from '../components/WalletConnect';
import StudentManagement from '../components/StudentManagement';

export default function Home() {
  const [connectedAccount, setConnectedAccount] = useState(null);

  const handleAccountChange = (account) => {
    setConnectedAccount(account);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                去中心化学生管理系统
              </h1>
              <p className="text-gray-600 mt-1">
                基于区块链的学生信息管理平台
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 钱包连接区域 */}
        <div className="mb-8">
          <WalletConnect onAccountChange={handleAccountChange} />
        </div>

        {/* 学生管理区域 */}
        <StudentManagement account={connectedAccount} />
      </main>

      {/* 页脚 */}
      <footer className="bg-white border-t mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-gray-600">
            <p>© 2024 去中心化学生管理系统. 基于以太坊区块链技术.</p>
            <p className="text-sm mt-2">
              请确保已连接到正确的本地区块链网络
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
