// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * @title StudentManagement
 * @dev 去中心化学生管理系统智能合约
 * <AUTHOR> Name
 */
contract StudentManagement {
    
    // 学生结构体
    struct Student {
        uint256 id;
        string name;
        uint256 age;
        string major;
        bool isActive;
    }
    
    // 存储所有学生的映射
    mapping(uint256 => Student) private students;
    
    // 存储所有学生ID的数组
    uint256[] private studentIds;
    
    // 合约所有者
    address public owner;
    
    // 事件定义
    event StudentAdded(uint256 indexed id, string name, uint256 age, string major);
    event StudentUpdated(uint256 indexed id, string name, uint256 age, string major);
    event StudentDeleted(uint256 indexed id);
    
    // 修饰符：只有所有者可以执行
    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner can perform this action");
        _;
    }
    
    // 修饰符：检查学生是否存在
    modifier studentExists(uint256 _id) {
        require(students[_id].isActive, "Student does not exist");
        _;
    }
    
    // 修饰符：检查学生是否不存在
    modifier studentNotExists(uint256 _id) {
        require(!students[_id].isActive, "Student already exists");
        _;
    }
    
    // 构造函数
    constructor() {
        owner = msg.sender;
    }
    
    /**
     * @dev 添加新学生
     * @param _id 学生ID
     * @param _name 学生姓名
     * @param _age 学生年龄
     * @param _major 学生专业
     */
    function addStudent(
        uint256 _id,
        string memory _name,
        uint256 _age,
        string memory _major
    ) public studentNotExists(_id) {
        require(_id > 0, "Student ID must be greater than 0");
        require(bytes(_name).length > 0, "Student name cannot be empty");
        require(_age > 0 && _age < 150, "Invalid age");
        require(bytes(_major).length > 0, "Student major cannot be empty");
        
        students[_id] = Student({
            id: _id,
            name: _name,
            age: _age,
            major: _major,
            isActive: true
        });
        
        studentIds.push(_id);
        
        emit StudentAdded(_id, _name, _age, _major);
    }
    
    /**
     * @dev 更新学生信息
     * @param _id 学生ID
     * @param _name 新的学生姓名
     * @param _age 新的学生年龄
     * @param _major 新的学生专业
     */
    function updateStudent(
        uint256 _id,
        string memory _name,
        uint256 _age,
        string memory _major
    ) public studentExists(_id) {
        require(bytes(_name).length > 0, "Student name cannot be empty");
        require(_age > 0 && _age < 150, "Invalid age");
        require(bytes(_major).length > 0, "Student major cannot be empty");
        
        students[_id].name = _name;
        students[_id].age = _age;
        students[_id].major = _major;
        
        emit StudentUpdated(_id, _name, _age, _major);
    }
    
    /**
     * @dev 删除学生（软删除）
     * @param _id 学生ID
     */
    function deleteStudent(uint256 _id) public studentExists(_id) {
        students[_id].isActive = false;
        
        // 从studentIds数组中移除
        for (uint256 i = 0; i < studentIds.length; i++) {
            if (studentIds[i] == _id) {
                studentIds[i] = studentIds[studentIds.length - 1];
                studentIds.pop();
                break;
            }
        }
        
        emit StudentDeleted(_id);
    }
    
    /**
     * @dev 获取单个学生信息
     * @param _id 学生ID
     * @return Student 学生信息
     */
    function getStudent(uint256 _id) public view studentExists(_id) returns (Student memory) {
        return students[_id];
    }
    
    /**
     * @dev 获取所有活跃学生
     * @return Student[] 所有活跃学生的数组
     */
    function getAllStudents() public view returns (Student[] memory) {
        Student[] memory activeStudents = new Student[](studentIds.length);
        uint256 count = 0;
        
        for (uint256 i = 0; i < studentIds.length; i++) {
            if (students[studentIds[i]].isActive) {
                activeStudents[count] = students[studentIds[i]];
                count++;
            }
        }
        
        // 创建正确大小的数组
        Student[] memory result = new Student[](count);
        for (uint256 i = 0; i < count; i++) {
            result[i] = activeStudents[i];
        }
        
        return result;
    }
    
    /**
     * @dev 获取学生总数
     * @return uint256 活跃学生总数
     */
    function getStudentCount() public view returns (uint256) {
        return studentIds.length;
    }
    
    /**
     * @dev 检查学生是否存在
     * @param _id 学生ID
     * @return bool 学生是否存在
     */
    function isStudentExists(uint256 _id) public view returns (bool) {
        return students[_id].isActive;
    }
    
    /**
     * @dev 转移合约所有权
     * @param _newOwner 新所有者地址
     */
    function transferOwnership(address _newOwner) public onlyOwner {
        require(_newOwner != address(0), "New owner cannot be zero address");
        owner = _newOwner;
    }
}
