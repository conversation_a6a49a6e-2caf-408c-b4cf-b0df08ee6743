import { useState, useEffect } from 'react';
import web3Service from '../utils/web3';
import { isContractConfigured } from '../config/contract';

export default function WalletConnect({ onAccountChange }) {
  const [account, setAccount] = useState(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    // 检查是否已经连接
    checkConnection();
    
    // 监听账户变化
    web3Service.onAccountsChanged(handleAccountsChanged);
    
    // 监听网络变化
    web3Service.onChainChanged(handleChainChanged);
  }, []);

  const checkConnection = async () => {
    try {
      const currentAccount = await web3Service.getCurrentAccount();
      if (currentAccount) {
        setAccount(currentAccount);
        onAccountChange?.(currentAccount);
      }
    } catch (error) {
      console.error('检查连接状态失败:', error);
    }
  };

  const handleAccountsChanged = (accounts) => {
    if (accounts.length === 0) {
      setAccount(null);
      onAccountChange?.(null);
      web3Service.disconnect();
    } else {
      setAccount(accounts[0]);
      onAccountChange?.(accounts[0]);
    }
  };

  const handleChainChanged = () => {
    // 网络变化时重新加载页面
    window.location.reload();
  };

  const connectWallet = async () => {
    if (!isContractConfigured()) {
      setError('合约地址未配置，请联系开发者配置合约地址');
      return;
    }

    setIsConnecting(true);
    setError('');

    try {
      const connectedAccount = await web3Service.connectWallet();
      setAccount(connectedAccount);
      onAccountChange?.(connectedAccount);
    } catch (error) {
      setError(error.message);
    } finally {
      setIsConnecting(false);
    }
  };

  const disconnectWallet = () => {
    web3Service.disconnect();
    setAccount(null);
    onAccountChange?.(null);
  };

  const formatAddress = (address) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  if (!web3Service.isMetaMaskInstalled()) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <h3 className="text-red-800 font-medium">MetaMask未安装</h3>
        <p className="text-red-600 text-sm mt-1">
          请安装MetaMask钱包扩展程序以使用此应用
        </p>
        <a
          href="https://metamask.io/download/"
          target="_blank"
          rel="noopener noreferrer"
          className="inline-block mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          下载MetaMask
        </a>
      </div>
    );
  }

  if (!isContractConfigured()) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="text-yellow-800 font-medium">合约未配置</h3>
        <p className="text-yellow-600 text-sm mt-1">
          请联系开发者在 src/config/contract.js 中配置合约地址
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded p-3 mb-4">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {!account ? (
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">连接钱包</h3>
          <p className="text-gray-600 text-sm mb-4">
            连接MetaMask钱包以使用学生管理系统
          </p>
          <button
            onClick={connectWallet}
            disabled={isConnecting}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isConnecting ? '连接中...' : '连接MetaMask'}
          </button>
        </div>
      ) : (
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">钱包已连接</h3>
            <p className="text-gray-600 text-sm">
              地址: {formatAddress(account)}
            </p>
          </div>
          <button
            onClick={disconnectWallet}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
          >
            断开连接
          </button>
        </div>
      )}
    </div>
  );
}
