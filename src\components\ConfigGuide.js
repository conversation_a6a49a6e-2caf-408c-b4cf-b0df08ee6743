import { useState } from 'react';

export default function ConfigGuide() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* 帮助按钮 */}
      <button
        onClick={() => setIsOpen(true)}
        className="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg transition-colors duration-200"
        title="配置帮助"
      >
        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </button>

      {/* 配置指南模态框 */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-900">配置指南</h2>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">1. 部署智能合约</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-sm text-gray-700 mb-2">在Remix IDE中部署智能合约：</p>
                    <ol className="list-decimal list-inside text-sm text-gray-600 space-y-1">
                      <li>打开 <code className="bg-gray-200 px-1 rounded">contracts/StudentManagement.sol</code></li>
                      <li>编译合约</li>
                      <li>连接到本地网络（Ganache）</li>
                      <li>部署合约并复制合约地址</li>
                    </ol>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">2. 配置合约地址</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-sm text-gray-700 mb-2">在配置文件中设置合约地址：</p>
                    <div className="bg-gray-800 text-green-400 p-3 rounded text-xs font-mono">
                      <div>// src/config/contract.js</div>
                      <div className="mt-2">export const CONTRACT_CONFIG = &#123;</div>
                      <div className="ml-2">STUDENT_MANAGEMENT_ADDRESS: "0x你的合约地址",</div>
                      <div>&#125;;</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">3. 配置MetaMask</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-sm text-gray-700 mb-2">添加本地网络到MetaMask：</p>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• 网络名称: Local Blockchain</li>
                      <li>• RPC URL: http://127.0.0.1:8545</li>
                      <li>• 链ID: 1337</li>
                      <li>• 货币符号: ETH</li>
                    </ul>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">4. 开始使用</h3>
                  <div className="bg-blue-50 rounded-lg p-4">
                    <p className="text-sm text-blue-700">
                      配置完成后，刷新页面并连接MetaMask钱包即可开始使用学生管理系统。
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => setIsOpen(false)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
                >
                  我知道了
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
